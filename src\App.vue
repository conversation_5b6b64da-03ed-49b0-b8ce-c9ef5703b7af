<template>
  <div class="h-screen w-screen flex items-center justify-center relative overflow-hidden transition-colors duration-1000" :style="{ backgroundColor: currentBgColor }">
    <!-- Floating fidget objects -->
    <div class="absolute inset-0 pointer-events-none z-10">
      <div
        v-for="fidget in fidgetObjects"
        :key="fidget.id"
        class="absolute cursor-pointer pointer-events-auto select-none transition-transform hover:scale-110"
        :style="{
          left: fidget.x + 'px',
          top: fidget.y + 'px',
          fontSize: fidget.size + 'px',
          transform: `rotate(${fidget.rotation}deg)`,
          animation: `float-${fidget.id % 3} ${fidget.speed}s infinite ease-in-out`
        }"
        @click="clickFidget(fidget)"
      >
        {{ fidget.emoji }}
      </div>
    </div>

    <!-- Random motivational messages -->
    <div class="absolute top-4 left-4 max-w-xs">
      <div v-if="currentMotivation" class="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-lg p-3 text-white/90 text-sm animate-pulse">
        {{ currentMotivation }}
      </div>
    </div>

    <!-- Button -->
     <div class="absolute top-4 left-1/2 -translate-x-1/2 px-4 py-1 bg-black/20 rounded-full text-white/70 backdrop-blur-sm">
      Počet kliknutí: {{ count }} | Streak: {{ currentStreak }}
     </div>

    <!-- Achievement notifications -->
    <div class="absolute top-20 right-4 max-w-sm">
      <div
        v-for="achievement in recentAchievements"
        :key="achievement.id"
        class="mb-2 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 backdrop-blur-sm rounded-lg p-3 text-white animate-bounce"
      >
        🏆 {{ achievement.title }}: {{ achievement.description }}
      </div>
    </div>

    <!-- Random facts/jokes -->
    <div class="absolute bottom-4 left-4 max-w-md">
      <div v-if="currentFact" class="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-sm rounded-lg p-3 text-white/90 text-sm">
        💡 {{ currentFact }}
      </div>
    </div>
    <div
      ref="btnEl"
      class="mar rounded-full p-4 h-64 w-64 bg-black flex justify-center items-center relative z-50 shadow shadow-white cursor-pointer select-none"
      :class="{ clicked: isClicked }"
      :style="{
        backgroundImage: `url(${isClicked ? maUrl : mbUrl})`,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        backgroundSize: 'cover'
      }"
      @click="handleClick"
    />

    <!-- Canvas overlay for cat confetti -->
    <canvas ref="canvasEl" class="pointer-events-none absolute inset-0 z-40 select-none" />

    <!-- Comic shout bubbles -->
    <div class="pointer-events-none absolute inset-0 z-50 select-none">
      <div
        v-for="b in bubbles"
        :key="b.id"
        class="comic-bubble select-none pointer-events-none"
        :style="{
          left: b.x + 'px',
          top: b.y + 'px',
          transform: `translate(-50%, -50%) rotate(${b.rot}deg)`
        }"
        @animationend="onBubbleEnd(b.id)"
      >
        {{ b.text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";

// Assets handled by your bundler (Vite/Webpack)
import maUrl from "@/assets/ma.png";
import mbUrl from "@/assets/mb.png";
import audioFile from "@/assets/margaret.wav";

/** --- Button click animation --- **/
const isClicked = ref(false);

const count = ref(0);

// ADHD-friendly features
const currentStreak = ref(0);
const currentBgColor = ref('#171717'); // neutral-900
const currentMotivation = ref('');
const currentFact = ref('');

// Fidget objects
interface FidgetObject {
  id: number;
  x: number;
  y: number;
  emoji: string;
  size: number;
  rotation: number;
  speed: number;
}
const fidgetObjects = ref<FidgetObject[]>([]);
let fidgetId = 0;

// Achievements
interface Achievement {
  id: number;
  title: string;
  description: string;
  unlocked: boolean;
}
const recentAchievements = ref<Achievement[]>([]);
let achievementId = 0;

// Fun data arrays
const motivationalMessages = [
  "You're doing great! Keep clicking! 🌟",
  "Every click is a step forward! 💪",
  "Your brain is amazing! 🧠✨",
  "Fidgeting is totally normal and healthy! 🎯",
  "You've got this! One click at a time! 🚀",
  "Taking breaks is important too! 🌸",
  "Your focus is like a superpower! ⚡",
  "Clicking is basically exercise for your finger! 💪",
  "You're building finger muscles! 🏋️‍♀️",
  "Margaret approves of your dedication! 🐱"
];

const randomFacts = [
  "Cats sleep 12-16 hours a day! 😴",
  "A group of cats is called a 'clowder'! 🐱",
  "Cats have 32 muscles in each ear! 👂",
  "The first computer bug was literally a bug! 🐛",
  "Honey never spoils! 🍯",
  "Octopuses have three hearts! 💙💙💙",
  "Bananas are berries, but strawberries aren't! 🍌",
  "A shrimp's heart is in its head! 🦐",
  "Wombat poop is cube-shaped! 📦",
  "Sea otters hold hands while sleeping! 🦦"
];

const achievements: = [
  { threshold: 10, title: "Getting Started", description: "First 10 clicks!" },
  { threshold: 50, title: "Warming Up", description: "50 clicks of dedication!" },
  { threshold: 100, title: "Century Club", description: "100 clicks achieved!" },
  { threshold: 250, title: "Quarter Master", description: "250 clicks of focus!" },
  { threshold: 500, title: "Half Millennium", description: "500 clicks! Wow!" },
  { threshold: 1000, title: "Thousand Clicker", description: "1000 clicks! Legend!" },
  { threshold: 2500, title: "Click Master", description: "2500 clicks! Incredible!" },
  { threshold: 5000, title: "Click Deity", description: "5000 clicks! You're unstoppable!" }
];

const fidgetEmojis = ['🌟', '✨', '💫', '🎈', '🎯', '🎪', '🎨', '🎭', '🎪', '🌈', '🦋', '🌸', '🍄', '🎀', '💎'];

const bgColors = [
  '#171717', // neutral-900 (default)
  '#1e1b4b', // indigo-900
  '#581c87', // purple-900
  '#831843', // pink-900
  '#7c2d12', // orange-900
  '#365314', // lime-900
  '#164e63', // cyan-900
  '#1e3a8a', // blue-900
];

// Sound variety
const soundVariations = ['margaret.wav']; // We'll add more if you have them

/** --- Audio (overlapping) via Web Audio API, mobile-safe --- **/
let audioCtx: AudioContext | null = null;
let audioBuffer: AudioBuffer | null = null;

async function ensureAudio() {
	// Create/resume context within the user gesture
	if (!audioCtx) {
		const Ctx = window.AudioContext || (window as any).webkitAudioContext;
		audioCtx = new Ctx();
	}
	if (audioCtx.state === "suspended") {
		await audioCtx.resume();
	}
	// Decode once (do it after resume for iOS)
	if (!audioBuffer) {
		const res = await fetch(audioFile);
		const arr = await res.arrayBuffer();
		audioBuffer = await new Promise<AudioBuffer>((resolve, reject) =>
			audioCtx!.decodeAudioData(arr, resolve, reject),
		);
	}
}

function playClickAudio() {
	if (!audioCtx || !audioBuffer) return;
	const src = audioCtx.createBufferSource();
	src.buffer = audioBuffer;
	src.connect(audioCtx.destination);
	src.start(0);
}

/** --- Confetti canvas & particles --- **/
const canvasEl = ref<HTMLCanvasElement | null>(null);
const btnEl = ref<HTMLElement | null>(null);
let ctx: CanvasRenderingContext2D | null = null;
let rafId: number | null = null;
let lastTs = 0;

interface Particle {
	x: number;
	y: number;
	vx: number;
	vy: number;
	life: number;
	ttl: number;
	size: number;
	rot: number;
	rotSpeed: number;
	char: string;
}
const particles: Particle[] = [];

/** Physics & visuals **/
const GRAVITY = 1400; // px/s^2 (downwards)
const COUNT = 36; // cats per burst
const EMOJI = "🐱";
const ANGLE_MIN = -140,
	ANGLE_MAX = -40; // wider up-left..up-right spread
const SPEED_MIN = 420,
	SPEED_MAX = 760; // more oomph

/** --- Comic shout bubbles (click-streak triggers) --- **/
type Shout = {
	text: string;
	minClicks: number;
	windowMs: number;
	cooldownMs?: number;
};
const SHOUTS: Shout[] = [
	{ text: "Meow!", minClicks: 3, windowMs: 700 },
	{ text: "Super meow!", minClicks: 5, windowMs: 900 },
	{ text: "Purrfect!", minClicks: 7, windowMs: 1100 },
	{ text: "NYA!", minClicks: 9, windowMs: 1200 },
	{ text: "CAT-astrophe!", minClicks: 12, windowMs: 1400 },
];
const lastTriggered = new Map<string, number>();

interface Bubble {
	id: number;
	text: string;
	x: number;
	y: number;
	rot: number;
}
const bubbles = ref<Bubble[]>([]);
let bubbleId = 0;
const MAX_BUBBLES = 18; // safety cap to avoid DOM thrash

// Click streak tracking
const clickTimes: number[] = [];
const MAX_WINDOW_MS = Math.max(...SHOUTS.map((s) => s.windowMs));

// Helper functions for ADHD features
function spawnFidgetObjects() {
  // Clear existing fidgets
  fidgetObjects.value = [];

  // Spawn 5-8 random fidget objects
  const count = 5 + Math.floor(Math.random() * 4);
  for (let i = 0; i < count; i++) {
    const margin = 100;
    fidgetObjects.value.push({
      id: ++fidgetId,
      x: margin + Math.random() * (window.innerWidth - 2 * margin),
      y: margin + Math.random() * (window.innerHeight - 2 * margin),
      emoji: fidgetEmojis[Math.floor(Math.random() * fidgetEmojis.length)],
      size: 20 + Math.random() * 15,
      rotation: Math.random() * 360,
      speed: 2 + Math.random() * 3
    });
  }
}

function clickFidget(fidget: FidgetObject) {
  // Remove the clicked fidget with a fun effect
  const index = fidgetObjects.value.findIndex(f => f.id === fidget.id);
  if (index !== -1) {
    fidgetObjects.value.splice(index, 1);

    // Spawn mini confetti at fidget location
    spawnCats(fidget.x, fidget.y, 8, 0);

    // Small count bonus
    count.value += 1;
    localStorage.setItem("count", count.value.toString());

    // Show a little achievement
    showMiniAchievement("Fidget Clicked!", "Found a floating friend! +1");
  }
}

function updateMotivationalMessage() {
  currentMotivation.value = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];

  // Change message every 8-15 seconds
  setTimeout(() => {
    currentMotivation.value = '';
    setTimeout(updateMotivationalMessage, 3000 + Math.random() * 5000);
  }, 8000 + Math.random() * 7000);
}

function updateRandomFact() {
  currentFact.value = randomFacts[Math.floor(Math.random() * randomFacts.length)];

  // Change fact every 12-20 seconds
  setTimeout(() => {
    currentFact.value = '';
    setTimeout(updateRandomFact, 5000 + Math.random() * 8000);
  }, 12000 + Math.random() * 8000);
}

function updateBackgroundColor() {
  // Change background based on click streaks
  const streakLevel = Math.min(Math.floor(currentStreak.value / 5), bgColors.length - 1);
  currentBgColor.value = bgColors[streakLevel];
}

function checkAchievements() {
  achievements.forEach(achievement => {
    if (count.value >= achievement.threshold && !achievement.unlocked) {
      achievement.unlocked = true;
      showAchievement(achievement.title, achievement.description);
    }
  });
}

function showAchievement(title: string, description: string) {
  const id = ++achievementId;
  recentAchievements.value.push({ id, title, description, unlocked: true });

  // Remove after 5 seconds
  setTimeout(() => {
    const index = recentAchievements.value.findIndex(a => a.id === id);
    if (index !== -1) recentAchievements.value.splice(index, 1);
  }, 5000);
}

function showMiniAchievement(title: string, description: string) {
  showAchievement(title, description);
}

onMounted(() => {
	// Preload images only; do NOT init audio here (iOS policy)
	new Image().src = maUrl;
	new Image().src = mbUrl;

	setupCanvas();
	window.addEventListener("resize", setupCanvas);

  count.value = localStorage.getItem("count") ? parseInt(localStorage.getItem("count")!) : 0;

  // Initialize ADHD-friendly features
  spawnFidgetObjects();
  updateMotivationalMessage();
  updateRandomFact();
  checkAchievements();

  // Respawn fidgets every 30-60 seconds
  setInterval(spawnFidgetObjects, 30000 + Math.random() * 30000);
});

onBeforeUnmount(() => {
	window.removeEventListener("resize", setupCanvas);
	if (rafId) cancelAnimationFrame(rafId);
	audioCtx?.close().catch(() => {});
});

function setupCanvas() {
	const c = canvasEl.value;
	if (!c) return;
	const dpr = Math.max(1, window.devicePixelRatio || 1);
	c.width = Math.floor(window.innerWidth * dpr);
	c.height = Math.floor(window.innerHeight * dpr);
	c.style.width = `${window.innerWidth}px`;
	c.style.height = `${window.innerHeight}px`;
	ctx = c.getContext("2d");
	// Make 1 unit == 1 CSS pixel
	ctx?.setTransform(dpr, 0, 0, dpr, 0, 0);
}

async function handleClick() {
	// Visual click
	isClicked.value = true;
	setTimeout(() => (isClicked.value = false), 400);

	// Track streak
	const now = Date.now();
	clickTimes.push(now);
	while (clickTimes.length && now - clickTimes[0] > MAX_WINDOW_MS)
		clickTimes.shift();

	// Update current streak (clicks in last 2 seconds)
	const recentClicks = clickTimes.filter(time => now - time <= 2000);
	currentStreak.value = recentClicks.length;

	// Confetti from (slightly outside) button center so they're not hidden
	const origin = buttonCenterInViewport();
	const btnW = btnEl.value?.getBoundingClientRect().width || 128;

	// More confetti for higher streaks!
	const confettiCount = COUNT + Math.min(currentStreak.value * 3, 50);
	spawnCats(origin.x, origin.y, confettiCount, btnW * 0.35);

	// Shout bubble if thresholds are met
	maybeShout(now, origin);

	// Start animation loop if needed
	if (!rafId) {
		lastTs = 0;
		rafId = requestAnimationFrame(tick);
	}

  count.value++;
  localStorage.setItem("count", count.value.toString());

	// Update background color based on streak
	updateBackgroundColor();

	// Check for achievements
	checkAchievements();

	// Random chance to spawn extra fidget objects on high streaks
	if (currentStreak.value > 10 && Math.random() < 0.3) {
		spawnFidgetObjects();
		showMiniAchievement("Streak Bonus!", "Extra fidgets spawned!");
	}

	// Special effects for milestone clicks
	if (count.value % 100 === 0) {
		// Extra celebration for hundreds
		spawnCats(origin.x, origin.y, 100, btnW * 0.5);
		showMiniAchievement("Century!", `${count.value} clicks reached!`);
	}

	// 🔊 Mobile-safe audio init + play inside this user gesture
	try {
		await ensureAudio();
		playClickAudio();
	} catch {
		// ignore audio errors (e.g., file blocked or decode failed)
	}
}

function buttonCenterInViewport() {
	const el = btnEl.value!;
	const rect = el.getBoundingClientRect();
	return { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 };
}

function countClicksIn(ms: number, now = Date.now()) {
	let i = clickTimes.length - 1,
		count = 0;
	for (; i >= 0; i--) {
		if (now - clickTimes[i] <= ms) count++;
		else break;
	}
	return count;
}

function maybeShout(now: number, origin: { x: number; y: number }) {
	const eligible = SHOUTS.filter(
		(s) => countClicksIn(s.windowMs, now) >= s.minClicks,
	).sort((a, b) => b.minClicks - a.minClicks); // prefer strongest
	if (!eligible.length) return;

	const pick = eligible[0];
	const last = lastTriggered.get(pick.text) ?? 0;
	const cooldown = pick.cooldownMs ?? Math.max(600, pick.windowMs);
	if (now - last < cooldown) return;

	spawnBubble(pick.text, origin.x, origin.y);
	lastTriggered.set(pick.text, now);
}

function spawnBubble(text: string, cx: number, cy: number) {
	// slight random offset above the button
	const offsetR = 40 + Math.random() * 40;
	const offsetAng = (-90 + (Math.random() - 0.5) * 40) * (Math.PI / 180);
	const x = cx + Math.cos(offsetAng) * offsetR;
	const y = cy + Math.sin(offsetAng) * offsetR - 10;

	const id = ++bubbleId;
	if (bubbles.value.length >= MAX_BUBBLES) bubbles.value.splice(0, 1);
	bubbles.value.push({
		id,
		text,
		x,
		y,
		rot: (Math.random() - 0.5) * 18,
	});
}

function onBubbleEnd(id: number) {
	const idx = bubbles.value.findIndex((b) => b.id === id);
	if (idx !== -1) bubbles.value.splice(idx, 1); // in-place removal so others persist
}

function randRange(min: number, max: number) {
	return min + Math.random() * (max - min);
}

function spawnCats(cx: number, cy: number, count: number, radialOffset = 0) {
	for (let i = 0; i < count; i++) {
		const deg = randRange(ANGLE_MIN, ANGLE_MAX);
		const ang = (deg * Math.PI) / 180;
		const speed = randRange(SPEED_MIN, SPEED_MAX);

		// Spawn on a small ring so initial cats aren't behind the button
		const spawnR = radialOffset
			? randRange(radialOffset * 0.6, radialOffset)
			: 0;
		const sx = cx + Math.cos(ang) * spawnR;
		const sy = cy + Math.sin(ang) * spawnR;

		const ttl = 1.0 + Math.random() * 0.7;
		const size = 22 + Math.random() * 16;

		particles.push({
			x: sx,
			y: sy,
			vx: Math.cos(ang) * speed,
			vy: Math.sin(ang) * speed,
			life: ttl,
			ttl,
			size,
			rot: Math.random() * Math.PI,
			rotSpeed: (Math.random() - 0.5) * 5,
			char: EMOJI,
		});
	}
}

function tick(ts: number) {
	if (!ctx || !canvasEl.value) return;
	const dt = lastTs ? (ts - lastTs) / 1000 : 0;
	lastTs = ts;

	// Clear canvas (CSS pixels)
	ctx.clearRect(0, 0, window.innerWidth, window.innerHeight);

	// Update & draw particles
	for (let i = particles.length - 1; i >= 0; i--) {
		const p = particles[i];
		p.vy += GRAVITY * dt;
		p.x += p.vx * dt;
		p.y += p.vy * dt;
		p.rot += p.rotSpeed * dt;
		p.life -= dt;

		const t = Math.max(0, p.life / p.ttl);
		const alpha = Math.min(1, Math.max(0, t * 1.1));

		ctx.save();
		ctx.globalAlpha = alpha;
		ctx.translate(p.x, p.y);
		ctx.rotate(p.rot);
		ctx.textAlign = "center";
		ctx.textBaseline = "middle";
		ctx.font = `${p.size}px sans-serif`;
		ctx.fillText(p.char, 0, 0);
		ctx.restore();

		// Cull dead or far-off particles
		if (
			p.life <= 0 ||
			p.y > window.innerHeight + 200 ||
			p.x < -200 ||
			p.x > window.innerWidth + 200
		) {
			particles.splice(i, 1);
		}
	}

	if (particles.length > 0) {
		rafId = requestAnimationFrame(tick);
	} else {
		rafId = null;
	}
}
</script>

<style scoped>
.mar {
  transition: all 0.2s ease-in-out;
  transform-origin: center center;
  box-sizing: border-box;
}
.mar.clicked {
  transform: scale(1.2);
  transform-origin: center center;
  box-sizing: border-box;
}

/* Comic bubble styling + pop animation */
.comic-bubble {
  position: absolute;
  padding: 6px 12px;
  font-weight: 900;
  font-size: 28px;
  line-height: 1;
  color: #ffeb3b;           /* bright yellow */
  -webkit-text-stroke: 2px #000; /* outline (WebKit) */
  text-shadow:
    2px 2px 0 #000,
    -2px 2px 0 #000,
    2px -2px 0 #000,
    -2px -2px 0 #000,
    0 0 8px rgba(0,0,0,0.35);
  letter-spacing: 1px;
  will-change: transform, opacity;
  animation: pop-in 0.9s ease-out forwards;
  user-select: none;
  pointer-events: none;
  white-space: nowrap;
}

@keyframes pop-in {
  0%   { transform: translate(-50%, -50%) scale(0.6); opacity: 0; }
  20%  { transform: translate(-50%, -50%) scale(1.15); opacity: 1; }
  60%  { transform: translate(-50%, -50%) scale(1.0); }
  100% { transform: translate(-50%, -50%) scale(0.9); opacity: 0; }
}

/* Floating animations for fidget objects */
@keyframes float-0 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-1 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-15px) rotate(120deg); }
  66% { transform: translateY(-25px) rotate(240deg); }
}

@keyframes float-2 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(90deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
  75% { transform: translateY(-15px) rotate(270deg); }
}

/* Pulse animation for motivational messages */
@keyframes pulse {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
}

/* Bounce animation for achievements */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

/* Gradient background animation */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style>
